{"name": "server", "version": "1.0.0", "description": "Server for 'The Salty Devs' Blog app", "main": "src/server.ts", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start": "tsx src/server.ts", "dev": "nodemon --watch src --ext ts --exec \"tsx src/server.ts\"", "prisma:generate": "prisma generate"}, "repository": {"type": "git", "url": "git+https://github.com/anshoolp-endure/The-Salty-Devs.git"}, "author": "<PERSON><PERSON><PERSON> and Rhythm Naik", "license": "ISC", "bugs": {"url": "https://github.com/anshoolp-endure/The-Salty-Devs/issues"}, "homepage": "https://github.com/anshoolp-endure/The-Salty-Devs#readme", "dependencies": {"@prisma/client": "^6.16.2", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.2", "esbuild": "^0.25.10", "express": "^5.1.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "the-salty-devs": "file:..", "zod": "^4.1.11", "zod-validation-error": "^4.0.2"}, "devDependencies": {"@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.5.2", "esbuild-register": "^3.6.0", "nodemon": "^3.1.10", "prisma": "^6.16.2", "ts-node": "^10.9.2", "tsx": "^4.20.5", "typescript": "^5.9.2"}}